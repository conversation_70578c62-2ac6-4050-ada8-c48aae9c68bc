load("@rules_java//java:defs.bzl", "java_library")
load("@rules_spring//springboot:springboot.bzl", "springboot")
load("@contrib_rules_jvm//java:defs.bzl", "java_test_suite")

java_library(
    name = "lib",
    srcs = glob(["src/main/java/**/*.java"]),
    resources = glob(["src/main/resources/**"]),
    # Must be workspace-root relative. Make resources land at classpath root.
    resource_strip_prefix = "services/user/src/main/resources",
    plugins = ["//:lombok_ap", "//:mapstruct_ap", "//:mapstruct_spi_protobuf_ap"],
    deps = [
        "//proto/user:grpc_java",

        "@maven//:org_springframework_boot_spring_boot_starter",
        "@maven//:io_github_danielliu1123_grpc_boot_starter",

        "//:lombok_lib",
        "//:mapstruct_lib",
    ],
    runtime_deps = [
        "@maven//:org_springframework_boot_spring_boot_jarmode_tools",
        "@maven//:org_springframework_boot_spring_boot_loader_tools",
    ],
)

springboot(
    name = "app",
    boot_app_class = "com.example.user.UserApp",
    java_library = ":lib",
    boot_launcher_class = 'org.springframework.boot.loader.launch.JarLauncher',
    include_git_properties_file = False,
)

java_test_suite(
    name = "test",
    srcs = glob(["src/test/java/**/*.java"]),
    runner = "junit5",
    test_suffixes = ["Test.java", "Tests.java", "IT.java"],
    deps = [
        ":lib",

        "@maven//:org_springframework_boot_spring_boot_starter_test",
        "@maven//:org_junit_platform_junit_platform_reporting",
    ],
)
