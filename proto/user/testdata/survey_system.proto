syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service SurveyService {
  rpc CreateSurvey(CreateSurveyRequest) returns (CreateSurveyResponse);
  rpc <PERSON>ur<PERSON>(TakeSurveyRequest) returns (TakeSurveyResponse);
  rpc GetSurveyResults(GetSurveyResultsRequest) returns (SurveyResults);
}

message Survey {
  string survey_id = 1;
  string title = 2;
  repeated Question questions = 3;
}

message Question {
  string question_id = 1;
  string text = 2;
  string type = 3;
  repeated string options = 4;
}

message Answer {
  string question_id = 1;
  string value = 2;
}

message SurveyResponse {
  string response_id = 1;
  string survey_id = 2;
  string user_id = 3;
  repeated Answer answers = 4;
  google.protobuf.Timestamp submitted_at = 5;
}

message SurveyResults {
  string survey_id = 1;
  repeated SurveyResponse responses = 2;
}

message CreateSurveyRequest {
  Survey survey = 1;
}

message CreateSurveyResponse {
  Survey survey = 1;
  string error_message = 2;
}

message TakeSurveyRequest {
  SurveyResponse response = 1;
}

message TakeSurveyResponse {
  SurveyResponse response = 1;
  string error_message = 2;
}

message GetSurveyResultsRequest {
  string survey_id = 1;
}
