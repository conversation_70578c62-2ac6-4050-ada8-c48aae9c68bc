syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service VehicleRentalService {
  rpc SearchVehicles(SearchVehiclesRequest) returns (SearchVehiclesResponse);
  rpc RentVehicle(RentVehicleRequest) returns (RentVehicleResponse);
  rpc GetRentalStatus(GetRentalStatusRequest) returns (Rental);
}

message Vehicle {
  string vehicle_id = 1;
  string make = 2;
  string model = 3;
  int32 year = 4;
  string type = 5;
  double price_per_day = 6;
  bool is_available = 7;
}

message Rental {
  string rental_id = 1;
  string vehicle_id = 2;
  string user_id = 3;
  google.protobuf.Timestamp start_date = 4;
  google.protobuf.Timestamp end_date = 5;
  string status = 6;
}

message SearchVehiclesRequest {
  string type = 1;
  google.protobuf.Timestamp start_date = 2;
  google.protobuf.Timestamp end_date = 3;
}

message SearchVehiclesResponse {
  repeated Vehicle vehicles = 1;
}

message RentVehicleRequest {
  string vehicle_id = 1;
  string user_id = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

message RentVehicleResponse {
  Rental rental = 1;
  string error_message = 2;
}

message GetRentalStatusRequest {
  string rental_id = 1;
}
