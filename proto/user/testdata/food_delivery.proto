syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service FoodDeliveryService {
  rpc PlaceOrder(PlaceOrderRequest) returns (PlaceOrderResponse);
  rpc TrackOrder(TrackOrderRequest) returns (stream OrderStatusUpdate);
  rpc GetRestaurantMenu(GetRestaurantMenuRequest) returns (Menu);
}

message Order {
  string order_id = 1;
  string user_id = 2;
  string restaurant_id = 3;
  repeated MenuItem items = 4;
  double total_price = 5;
  string status = 6;
  google.protobuf.Timestamp estimated_delivery_time = 7;
}

message MenuItem {
  string item_id = 1;
  string name = 2;
  double price = 3;
  int32 quantity = 4;
}

message Restaurant {
  string restaurant_id = 1;
  string name = 2;
  string address = 3;
  Menu menu = 4;
}

message Menu {
  string restaurant_id = 1;
  repeated MenuItem items = 2;
}

message OrderStatusUpdate {
  string order_id = 1;
  string status = 2;
  google.protobuf.Timestamp timestamp = 3;
}

message PlaceOrderRequest {
  string user_id = 1;
  string restaurant_id = 2;
  repeated MenuItem items = 3;
}

message PlaceOrderResponse {
  Order order = 1;
  string error_message = 2;
}

message TrackOrderRequest {
  string order_id = 1;
}

message GetRestaurantMenuRequest {
  string restaurant_id = 1;
}
