syntax = "proto3";

package user.testdata;



option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service InventoryManagementService {
  rpc AddItem(AddItemRequest) returns (AddItemResponse);
  rpc RemoveItem(RemoveItemRequest) returns (RemoveItemResponse);
  rpc GetInventory(GetInventoryRequest) returns (GetInventoryResponse);
}

message Item {
  string item_id = 1;
  string name = 2;
  int32 quantity = 3;
  double price = 4;
}

message AddItemRequest {
  Item item = 1;
}

message AddItemResponse {
  Item item = 1;
  string error_message = 2;
}

message RemoveItemRequest {
  string item_id = 1;
  int32 quantity = 2;
}

message RemoveItemResponse {
  Item item = 1;
  string error_message = 2;
}

message GetInventoryRequest {}

message GetInventoryResponse {
  repeated Item items = 1;
}
