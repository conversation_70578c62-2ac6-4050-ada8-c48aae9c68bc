syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service LibraryService {
  rpc SearchBooks(SearchBooksRequest) returns (SearchBooksResponse);
  rpc BorrowBook(BorrowBookRequest) returns (BorrowBookResponse);
  rpc ReturnBook(ReturnBookRequest) returns (ReturnBookResponse);
}

message Book {
  string book_id = 1;
  string title = 2;
  string author = 3;
  string isbn = 4;
  bool is_borrowed = 5;
}

message BorrowRecord {
  string record_id = 1;
  string book_id = 2;
  string user_id = 3;
  google.protobuf.Timestamp borrowed_date = 4;
  google.protobuf.Timestamp due_date = 5;
}

message SearchBooksRequest {
  string query = 1;
}

message SearchBooksResponse {
  repeated Book books = 1;
}

message BorrowBookRequest {
  string book_id = 1;
  string user_id = 2;
}

message BorrowBookResponse {
  BorrowRecord record = 1;
  string error_message = 2;
}

message ReturnBookRequest {
  string book_id = 1;
  string user_id = 2;
}

message ReturnBookResponse {
  Book book = 1;
  string error_message = 2;
}
