syntax = "proto3";

package user.testdata;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/example/bazel-example/proto/user/testdata";
option java_multiple_files = true;

service TelemedicineService {
  rpc StartConsultation(StartConsultationRequest) returns (StartConsultationResponse);
  rpc SendVitals(stream VitalSigns) returns (SendVitalsResponse);
  rpc EndConsultation(EndConsultationRequest) returns (EndConsultationResponse);
}

message Consultation {
  string consultation_id = 1;
  string patient_id = 2;
  string doctor_id = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
}

message VitalSigns {
  string consultation_id = 1;
  double temperature_celsius = 2;
  int32 heart_rate = 3;
  int32 blood_pressure_systolic = 4;
  int32 blood_pressure_diastolic = 5;
  google.protobuf.Timestamp timestamp = 6;
}

message StartConsultationRequest {
  string patient_id = 1;
  string doctor_id = 2;
}

message StartConsultationResponse {
  Consultation consultation = 1;
  string error_message = 2;
}

message SendVitalsResponse {
  bool success = 1;
  string error_message = 2;
}

message EndConsultationRequest {
  string consultation_id = 1;
}

message EndConsultationResponse {
  Consultation consultation = 1;
  string error_message = 2;
}
